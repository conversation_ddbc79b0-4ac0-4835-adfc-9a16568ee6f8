# 修改显示时间窗口指南

## 概述

本文档说明如何修改监控系统的时间窗口显示范围。当前系统默认显示最近10分钟的数据，本指南将说明如何修改为其他时间范围（如20分钟、30分钟等）。

## 系统架构

- **后端（C++）**：负责数据采集和存储，使用循环缓冲区管理数据
- **前端（QML）**：负责数据显示和用户界面，控制时间轴和图表渲染
- **采样间隔**：固定为3秒/点，不建议修改

## 修改步骤

### 1. 修改缓冲区大小（必须）

**文件**：`monitoring_datasource.h`

**位置**：第119行左右

**当前代码**：
```cpp
static const int MAX_DATA_POINTS = 200;  // 10分钟，每3秒一个点
```

**修改示例**：
```cpp
// 显示20分钟
static const int MAX_DATA_POINTS = 400;  // 20分钟，每3秒一个点

// 显示30分钟
static const int MAX_DATA_POINTS = 600;  // 30分钟，每3秒一个点

// 显示60分钟
static const int MAX_DATA_POINTS = 1200; // 60分钟，每3秒一个点
```

**计算公式**：
```
MAX_DATA_POINTS = (目标分钟数 × 60秒) ÷ 3秒
```

### 2. 修改前端窗口大小（必须）

**文件**：`MonitoringSystem.qml`

**位置**：ValueAxis 配置中的 windowSize 属性

**当前代码**：
```qml
property double windowSize: 10.0  // 10分钟窗口
```

**修改示例**：
```qml
// 显示20分钟
property double windowSize: 20.0  // 20分钟窗口

// 显示30分钟
property double windowSize: 30.0  // 30分钟窗口

// 显示60分钟
property double windowSize: 60.0  // 60分钟窗口
```

### 3. 调整X轴刻度（可选）

**文件**：`MonitoringSystem.qml`

**位置**：ValueAxis 配置中的 tickCount 属性

**当前代码**：
```qml
tickCount: 6  // 10分钟：每2分钟一个刻度 (0,2,4,6,8,10)
```

**修改建议**：

| 时间窗口 | tickCount | 刻度间隔 | 刻度点 |
|----------|-----------|----------|--------|
| 10分钟   | 6         | 2分钟    | 0,2,4,6,8,10 |
| 20分钟   | 6         | 4分钟    | 0,4,8,12,16,20 |
| 20分钟   | 11        | 2分钟    | 0,2,4,6,8,10,12,14,16,18,20 |
| 30分钟   | 7         | 5分钟    | 0,5,10,15,20,25,30 |
| 60分钟   | 7         | 10分钟   | 0,10,20,30,40,50,60 |

## 常见时间窗口配置

### 20分钟窗口
```cpp
// monitoring_datasource.h
static const int MAX_DATA_POINTS = 400;  // 20分钟，每3秒一个点
```

```qml
// MonitoringSystem.qml
property double windowSize: 20.0  // 20分钟窗口
tickCount: 6  // 每4分钟一个刻度
```

### 30分钟窗口
```cpp
// monitoring_datasource.h
static const int MAX_DATA_POINTS = 600;  // 30分钟，每3秒一个点
```

```qml
// MonitoringSystem.qml
property double windowSize: 30.0  // 30分钟窗口
tickCount: 7  // 每5分钟一个刻度
```

### 60分钟窗口
```cpp
// monitoring_datasource.h
static const int MAX_DATA_POINTS = 1200; // 60分钟，每3秒一个点
```

```qml
// MonitoringSystem.qml
property double windowSize: 60.0  // 60分钟窗口
tickCount: 7  // 每10分钟一个刻度
```

## 注意事项

### 内存使用
- 每个数据点约占用48字节
- 400个点（20分钟）≈ 19KB
- 1200个点（60分钟）≈ 58KB
- 对于工业应用，这些内存使用量都是可接受的

### 性能影响
- 数据点增加会略微影响图表渲染性能
- 建议不超过2000个数据点（约100分钟）
- 如需更长时间显示，考虑数据抽样或分页显示

### 不需要修改的部分
- ✅ 采样间隔（3秒/点）
- ✅ 数据结构定义
- ✅ 时间计算逻辑
- ✅ 滚动显示逻辑

## 验证修改

修改完成后，重新编译运行程序，验证：

1. **数据采集**：确认数据正常采集和存储
2. **时间轴**：检查X轴显示范围是否正确
3. **滚动效果**：运行超过设定时间后，确认滚动显示正常
4. **内存使用**：监控内存使用情况，确保在合理范围内

## 故障排除

### 编译错误
- 检查 `MAX_DATA_POINTS` 是否为正整数
- 确认语法正确，分号完整

### 显示异常
- 检查 `windowSize` 数值是否与缓冲区大小匹配
- 确认 `tickCount` 设置合理

### 性能问题
- 如果图表卡顿，考虑减少数据点数量
- 检查是否有内存泄漏

---

**最后更新**：2025-07-30
**适用版本**：当前监控系统版本
